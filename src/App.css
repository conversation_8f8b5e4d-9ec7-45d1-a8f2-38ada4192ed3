/* CSS Custom Properties for Theme */
:root {
  /* Dark theme (default) */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #808080;
  --accent-primary: #ff6b35;
  --accent-secondary: #4ecdc4;
  --accent-tertiary: #45b7d1;
  --border-color: #333333;
  --shadow: rgba(0, 0, 0, 0.3);
  --gradient-primary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  --gradient-secondary: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #495057;
  --text-muted: #6c757d;
  --accent-primary: #ff6b35;
  --accent-secondary: #4ecdc4;
  --accent-tertiary: #45b7d1;
  --border-color: #dee2e6;
  --shadow: rgba(0, 0, 0, 0.1);
  --gradient-primary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  --gradient-secondary: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  text-align: center;
}

#root {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease;
  text-align: center;
}

.container {
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
}

/* Responsive container widths - ALL FULL WIDTH */
@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
    width: 100%;
  }
}

@media (min-width: 1200px) {
  .container {
    padding: 0 3rem;
    width: 100%;
  }
}

@media (min-width: 1600px) {
  .container {
    padding: 0 4rem;
    width: 100%;
  }
}

@media (min-width: 1920px) {
  .container {
    padding: 0 6rem;
    width: 100%;
  }
}

@media (min-width: 2560px) {
  .container {
    padding: 0 8rem;
    width: 100%;
  }
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: all 0.3s ease;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-primary);
}

.theme-toggle {
  background: none;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  border-color: var(--accent-primary);
  transform: scale(1.1);
}

/* Typography */
.section-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--text-primary);
}

.hero-title {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-align: center;
}

.highlight {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  margin-bottom: 3rem;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 4px 15px var(--shadow);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--accent-primary);
  background-color: var(--bg-secondary);
  transform: translateY(-2px);
}

/* Hero Section */
.hero {
  padding: 8rem 0 6rem;
  text-align: center;
  background: radial-gradient(ellipse at center, var(--bg-secondary) 0%, var(--bg-primary) 70%);
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

@media (min-width: 1200px) {
  .hero-content {
    max-width: 80%;
  }
}

@media (min-width: 1400px) {
  .hero-content {
    max-width: 75%;
  }
}

@media (min-width: 1920px) {
  .hero-content {
    max-width: 70%;
  }
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Problem Section */
.problem {
  padding: 6rem 0;
  background-color: var(--bg-secondary);
  text-align: center;
}

.problem-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

@media (min-width: 1200px) {
  .problem-list {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1600px) {
  .problem-list {
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }
}

.problem-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--bg-tertiary);
  border-radius: 16px;
  transition: transform 0.3s ease;
  text-align: left;
}

.problem-item:hover {
  transform: translateY(-5px);
}

.problem-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.problem-item p {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.problem-tagline {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-primary);
  font-style: italic;
}

/* Difference Section */
.difference {
  padding: 6rem 0;
  background-color: var(--bg-primary);
  text-align: center;
}

.difference-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

@media (min-width: 1200px) {
  .difference-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1600px) {
  .difference-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }
}

.difference-card {
  text-align: center;
  padding: 3rem 2rem;
  background-color: var(--bg-secondary);
  border-radius: 20px;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  opacity: 0;
  transform: translateY(50px);
}

.difference-grid.animate-in .difference-card {
  animation: slideInUp 0.8s ease forwards;
}

.difference-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow);
  border-color: var(--accent-primary);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.difference-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.difference-card p {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

/* Scenarios Section */
.scenarios {
  padding: 6rem 0;
  background-color: var(--bg-secondary);
  text-align: center;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

@media (min-width: 1200px) {
  .scenarios-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1600px) {
  .scenarios-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 3rem;
  }
}

@media (min-width: 2000px) {
  .scenarios-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 4rem;
  }
}

.scenario-card {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: 16px;
  border-left: 4px solid var(--accent-secondary);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(50px) scale(0.9);
}

.scenarios-grid.animate-in .scenario-card {
  animation: slideInScale 0.8s ease forwards;
}

.scenario-card:nth-child(1) {
  animation-delay: 0.1s;
}

.scenario-card:nth-child(2) {
  animation-delay: 0.2s;
}

.scenario-card:nth-child(3) {
  animation-delay: 0.3s;
}

.scenario-card:nth-child(4) {
  animation-delay: 0.4s;
}

.scenario-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px var(--shadow);
}

.scenario-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.scenario-text {
  font-size: 1.1rem;
  color: var(--text-secondary);
  font-style: italic;
  line-height: 1.5;
}

/* Vision Section */
.vision {
  padding: 6rem 0;
  background-color: var(--bg-primary);
  text-align: center;
}

.vision-quote {
  max-width: 800px;
  margin: 0 auto;
}

@media (min-width: 1200px) {
  .vision-quote {
    max-width: 80%;
  }
}

@media (min-width: 1400px) {
  .vision-quote {
    max-width: 75%;
  }
}

@media (min-width: 1920px) {
  .vision-quote {
    max-width: 70%;
  }
}

.vision-quote blockquote {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 600;
  color: var(--accent-primary);
  margin-bottom: 2rem;
  position: relative;
  padding: 2rem;
  background-color: var(--bg-secondary);
  border-radius: 20px;
  border-left: 5px solid var(--accent-primary);
}

.vision-quote blockquote::before {
  content: '"';
  font-size: 4rem;
  position: absolute;
  top: -10px;
  left: 20px;
  color: var(--accent-primary);
  opacity: 0.3;
}

.vision-subtext {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Animations */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInScale {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

.card-icon:hover {
  animation: pulse 1s ease-in-out;
}

.scenario-icon:hover {
  animation: bounce 1s ease-in-out;
}

/* Enhanced micro-interactions */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.theme-toggle {
  position: relative;
}

.theme-toggle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: var(--accent-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: -1;
}

.theme-toggle:hover::after {
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

/* Survey Section */
.survey {
  padding: 6rem 0;
  background-color: var(--bg-secondary);
  text-align: center;
}

.survey-subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.survey-form {
  max-width: 600px;
  margin: 0 auto;
  background-color: var(--bg-primary);
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px var(--shadow);
  text-align: left;
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #e74c3c;
}

.error-text {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  display: block;
}



.submit-btn {
  width: 100%;
  margin-top: 1rem;
  font-size: 1.2rem;
  padding: 1.2rem;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Footer */
.footer {
  padding: 4rem 0 2rem;
  background-color: var(--bg-primary);
  text-align: center;
  border-top: 1px solid var(--border-color);
}

.footer-tagline {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.footer-credits {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.footer-copyright {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
    width: 100%;
    max-width: none;
  }

  .hero {
    padding: 6rem 0 4rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    min-width: 250px;
  }

  .difference-grid,
  .scenarios-grid {
    grid-template-columns: 1fr;
  }

  .problem-list {
    grid-template-columns: 1fr;
  }

  .survey-form {
    padding: 2rem 1.5rem;
  }

  .header .container {
    padding: 1rem;
    width: 100%;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
    width: 100%;
    max-width: none;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .btn {
    min-width: 200px;
    padding: 0.8rem 1.5rem;
  }

  .survey-form {
    padding: 1.5rem 1rem;
  }

  .header .container {
    padding: 1rem 0.5rem;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.btn:focus,
.theme-toggle:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .scenario-card {
    opacity: 1;
    transform: none;
    animation: none;
  }
}